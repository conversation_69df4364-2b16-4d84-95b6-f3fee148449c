package com.zrcoding.barbcker.domain.utils

import com.zrcoding.barbcker.domain.models.NetworkErrors
import dev.gitlive.firebase.FirebaseException
import dev.gitlive.firebase.auth.FirebaseAuthException
import dev.gitlive.firebase.firestore.FirebaseFirestoreException

/**
 * Utility class to categorize exceptions into appropriate NetworkErrors
 */
object NetworkErrorHandler {
    
    /**
     * Categorizes an exception into the appropriate NetworkError type
     * @param exception The exception to categorize
     * @param networkConnectivity Network connectivity checker
     * @return The appropriate NetworkError
     */
    suspend fun categorizeError(
        exception: Exception,
        networkConnectivity: NetworkConnectivity
    ): NetworkErrors {
        // First check if there's no internet connection
        if (!networkConnectivity.isConnected()) {
            return NetworkErrors.NO_INTERNET
        }
        
        return when (exception) {
            // Firebase Auth specific errors
            is FirebaseAuthException -> when {
                exception.message?.contains("network", ignoreCase = true) == true -> NetworkErrors.NO_INTERNET
                exception.message?.contains("timeout", ignoreCase = true) == true -> NetworkErrors.TIMEOUT
                exception.message?.contains("permission", ignoreCase = true) == true -> NetworkErrors.PERMISSION_DENIED
                exception.message?.contains("unauthorized", ignoreCase = true) == true -> NetworkErrors.AUTHENTICATION_FAILED
                else -> NetworkErrors.AUTHENTICATION_FAILED
            }
            
            // Firebase Firestore specific errors
            is FirebaseFirestoreException -> when {
                exception.message?.contains("network", ignoreCase = true) == true -> NetworkErrors.NO_INTERNET
                exception.message?.contains("timeout", ignoreCase = true) == true -> NetworkErrors.TIMEOUT
                exception.message?.contains("permission", ignoreCase = true) == true -> NetworkErrors.PERMISSION_DENIED
                exception.message?.contains("unavailable", ignoreCase = true) == true -> NetworkErrors.SERVER_ERROR
                else -> NetworkErrors.SERVER_ERROR
            }
            
            // General Firebase errors
            is FirebaseException -> when {
                exception.message?.contains("network", ignoreCase = true) == true -> NetworkErrors.NO_INTERNET
                exception.message?.contains("timeout", ignoreCase = true) == true -> NetworkErrors.TIMEOUT
                else -> NetworkErrors.SERVER_ERROR
            }
            
            // Network-related exceptions (common patterns)
            else -> when {
                exception.message?.contains("network", ignoreCase = true) == true -> NetworkErrors.NO_INTERNET
                exception.message?.contains("timeout", ignoreCase = true) == true -> NetworkErrors.TIMEOUT
                exception.message?.contains("connection", ignoreCase = true) == true -> NetworkErrors.NO_INTERNET
                exception.message?.contains("host", ignoreCase = true) == true -> NetworkErrors.NO_INTERNET
                exception.message?.contains("socket", ignoreCase = true) == true -> NetworkErrors.NO_INTERNET
                exception.message?.contains("ssl", ignoreCase = true) == true -> NetworkErrors.SERVER_ERROR
                exception.message?.contains("certificate", ignoreCase = true) == true -> NetworkErrors.SERVER_ERROR
                exception.message?.contains("unauthorized", ignoreCase = true) == true -> NetworkErrors.AUTHENTICATION_FAILED
                exception.message?.contains("forbidden", ignoreCase = true) == true -> NetworkErrors.PERMISSION_DENIED
                exception.message?.contains("server", ignoreCase = true) == true -> NetworkErrors.SERVER_ERROR
                else -> NetworkErrors.UNKNOWN
            }
        }
    }
}
