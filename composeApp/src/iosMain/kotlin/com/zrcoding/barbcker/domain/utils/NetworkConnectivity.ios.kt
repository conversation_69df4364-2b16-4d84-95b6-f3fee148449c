package com.zrcoding.barbcker.domain.utils

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import platform.Foundation.NSURLRequest
import platform.Foundation.NSURLSession
import platform.Foundation.NSURL
import platform.Foundation.NSError
import platform.Foundation.NSURLResponse
import platform.Foundation.NSData
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

class IosNetworkConnectivity : NetworkConnectivity {
    
    override suspend fun isConnected(): Boolean = withContext(Dispatchers.Default) {
        try {
            suspendCoroutine { continuation ->
                val url = NSURL.URLWithString("https://www.google.com")!!
                val request = NSURLRequest.requestWithURL(url)
                request.setTimeoutInterval(5.0)
                
                val task = NSURLSession.sharedSession.dataTaskWithRequest(request) { data, response, error ->
                    continuation.resume(error == null && response != null)
                }
                task.resume()
            }
        } catch (e: Exception) {
            false
        }
    }
}
