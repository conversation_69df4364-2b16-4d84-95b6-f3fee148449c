package com.zrcoding.barbcker.domain.models

sealed interface Resource<out T, out E : Error> {
    data class Success<out T>(val data: T) : Resource<T, Nothing>
    data class Failure<out E : Error>(val error: E) : Resource<Nothing, E>
}

interface Error

enum class NetworkErrors {
    NO_INTERNET,
    TIMEOUT,
    SERVER_ERROR,
    AUTHENTICATION_FAILED,
    PERMISSION_DENIED,
    UNKNOWN
}

enum class AuthStatus {
    COMPLETED,
    SHOULD_COMPLETE_ACCOUNT
}

sealed interface AuthErrors : Error {
    data class Network(val error: NetworkErrors) : AuthErrors
    data object ShouldRelogIn : AuthErrors
}

fun <V, E : Error> success(value: V): Resource<V, E> = Resource.Success(value)

fun <V, E : Error> failure(value: E): Resource<V, E> = Resource.Failure(value)

fun <V, E : Error> Resource<V, E>.getOrNull(): V? = (this as? Resource.Success)?.data

fun <V, E : Error> Resource<V, E>.errorOrNull(): E? = (this as? Resource.Failure)?.error

fun <V, E : Error> Resource<V, E>.isSuccess(): Boolean = this is Resource.Success