package com.zrcoding.barbcker.presentation.features.app

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tweener.passage.Passage
import com.tweener.passage.model.AppleGatekeeperConfiguration
import com.tweener.passage.model.GoogleGatekeeperAndroidConfiguration
import com.tweener.passage.model.GoogleGatekeeperConfiguration
import com.zrcoding.barbcker.presentation.design_system.theme.BarbckerTheme
import com.zrcoding.barbcker.presentation.design_system.utils.SnackBarHostForSnackBarController
import com.zrcoding.barbcker.presentation.features.navigation.BarbckerNavHost
import dev.gitlive.firebase.Firebase
import org.koin.compose.koinInject
import org.koin.compose.viewmodel.koinViewModel

@Composable
fun App(appViewModel: AppViewModel = koinViewModel()) {
    val passage: Passage = koinInject()
    LaunchedEffect(passage) {
        passage.initialize(
            gatekeeperConfigurations = listOf(
                GoogleGatekeeperConfiguration(
                    serverClientId = "************-d80dqau2tqek5p5e1vig6mumec39hdv7.apps.googleusercontent.com",
                    android = GoogleGatekeeperAndroidConfiguration(
                        filterByAuthorizedAccounts = false,
                        autoSelectEnabled = true,
                        maxRetries = 1
                    )
                ),
                AppleGatekeeperConfiguration(),
            ),
            firebase = Firebase
        )
    }
    passage.bindToView()
    val startDestination = appViewModel.startDestination.collectAsStateWithLifecycle().value
    BarbckerTheme {
        Scaffold(
            snackbarHost = { SnackBarHostForSnackBarController() }
        ) {
            when (startDestination) {
                StartDestination.Idle -> CircularProgressIndicator()
                is StartDestination.Screen -> BarbckerNavHost(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(MaterialTheme.colorScheme.background),
                    startDestination = startDestination,
                )
            }
        }
    }
}