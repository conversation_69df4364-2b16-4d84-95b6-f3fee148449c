package com.zrcoding.barbcker.presentation.features.di

import com.zrcoding.barbcker.presentation.features.app.AppViewModel
import com.zrcoding.barbcker.presentation.features.auth.authModule
import com.zrcoding.barbcker.presentation.features.complete_account.CompleteAccountViewModel
import org.koin.core.module.dsl.viewModelOf
import org.koin.dsl.module

val presentationModules = module {
    viewModelOf(::AppViewModel)
    viewModelOf(::CompleteAccountViewModel)
    includes(authModule)
}